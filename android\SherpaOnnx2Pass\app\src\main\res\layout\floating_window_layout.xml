<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <!-- 悬浮按钮主体 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cv_floating_button"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_gravity="center"
        app:cardCornerRadius="28dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="#2196F3">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- 录音图标 -->
            <ImageView
                android:id="@+id/iv_mic_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:src="@android:drawable/ic_btn_speak_now"
                android:tint="@android:color/white"
                android:contentDescription="录音按钮" />

            <!-- 脉动动画背景 -->
            <View
                android:id="@+id/view_pulse_background"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/floating_button_pulse"
                android:visibility="gone" />

        </FrameLayout>

    </androidx.cardview.widget.CardView>

    <!-- 操作菜单（长按显示） -->
    <LinearLayout
        android:id="@+id/ll_action_menu"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="70dp"
        android:background="@drawable/floating_menu_background"
        android:orientation="vertical"
        android:padding="8dp"
        android:visibility="gone">

        <!-- 打开主应用 -->
        <TextView
            android:id="@+id/tv_open_app"
            android:layout_width="120dp"
            android:layout_height="40dp"
            android:background="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:text="📱 打开应用"
            android:textColor="@android:color/black"
            android:textSize="14sp" />

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginVertical="4dp"
            android:background="#E0E0E0" />

        <!-- 关闭悬浮窗 -->
        <TextView
            android:id="@+id/tv_close_floating"
            android:layout_width="120dp"
            android:layout_height="40dp"
            android:background="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:text="❌ 关闭悬浮窗"
            android:textColor="@android:color/black"
            android:textSize="14sp" />

    </LinearLayout>

</FrameLayout>
