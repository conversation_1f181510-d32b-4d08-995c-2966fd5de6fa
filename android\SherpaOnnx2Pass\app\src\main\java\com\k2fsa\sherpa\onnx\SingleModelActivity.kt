package com.k2fsa.sherpa.onnx

import android.Manifest
import android.content.BroadcastReceiver
import android.content.ClipData
import android.content.ClipboardManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.OpenableColumns
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.*
import org.json.JSONObject
import org.json.JSONArray
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 单模型ASR引擎测试Activity - 优化UI版本
 * 参考FunASR的单模型实现，使用一个模型完成所有识别任务
 * 复用优化版的UI布局和功能，但保持单模型架构
 */
class SingleModelActivity : AppCompatActivity(), SingleModelASREngine.ASRListener {

    companion object {
        private const val TAG = "SingleModelActivity"
        private const val REQUEST_RECORD_AUDIO_PERMISSION = 200
        private const val SAMPLE_RATE = 16000
    }

    private val permissions: Array<String> = arrayOf(Manifest.permission.RECORD_AUDIO)

    // UI组件 - 适配新的Apple风格布局
    private lateinit var btnRecord: com.google.android.material.button.MaterialButton
    private lateinit var btnClear: com.google.android.material.button.MaterialButton
    private lateinit var btnSummary: com.google.android.material.button.MaterialButton
    private lateinit var btnSettings: android.widget.ImageButton
    private lateinit var btnMeetingRecords: android.widget.ImageButton
    private lateinit var btnImportAudio: com.google.android.material.button.MaterialButton
    private lateinit var tvStatus: TextView
    private lateinit var tvResults: TextView
    private lateinit var tvWordCount: TextView
    private lateinit var tvRecordingStatus: TextView
    private lateinit var llActions: LinearLayout

    // ASR引擎
    private lateinit var asrEngine: SingleModelASREngine
    private var isInitialized = false

    // 自动优化设置
    private var autoOptimize = true

    // 会议记录管理器
    private lateinit var meetingRecordManager: MeetingRecordManager
    private lateinit var audioRecordingManager: AudioRecordingManager

    // 当前录音文件路径
    private var currentAudioFilePath: String? = null

    // 音频录制服务相关
    private var audioRecordingService: AudioRecordingService? = null
    private var isServiceBound = false
    private val isRecording = AtomicBoolean(false)

    // 悬浮窗服务相关
    private var floatingWindowService: FloatingWindowService? = null
    private var isFloatingServiceBound = false

    // 结果管理
    private val recognitionResults = StringBuilder()
    private var wordCount = 0
    private var recognitionCount = 0
    private var sessionStartTime = 0L
    private val timeHandler = Handler(Looper.getMainLooper())
    private var timeUpdateRunnable: Runnable? = null

    // 当前会话的会议记录ID（用于更新优化内容和总结）
    private var currentMeetingRecordId: String? = null

    // 广播接收器 - 接收悬浮窗的录音切换指令
    private val recordingToggleReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.k2fsa.sherpa.onnx.TOGGLE_RECORDING") {
                Log.d(TAG, "收到悬浮窗录音切换指令")
                toggleRecording()
            }
        }
    }
    
    // 悬浮窗服务连接管理
    private val floatingServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.i(TAG, "FloatingWindowService connected")
            val binder = service as FloatingWindowService.FloatingWindowBinder
            floatingWindowService = binder.getService()
            isFloatingServiceBound = true
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Log.i(TAG, "FloatingWindowService disconnected")
            floatingWindowService = null
            isFloatingServiceBound = false
        }
    }

    // 音频录制服务连接管理
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.i(TAG, "AudioRecordingService connected")
            val binder = service as AudioRecordingService.AudioRecordingBinder
            audioRecordingService = binder.getService()
            isServiceBound = true
            
            // 设置回调和依赖
            audioRecordingService?.setCallback(audioRecordingCallback)
            audioRecordingService?.setAsrEngine(asrEngine)
            audioRecordingService?.setAudioRecordingManager(audioRecordingManager)
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            Log.i(TAG, "AudioRecordingService disconnected")
            audioRecordingService = null
            isServiceBound = false
        }
    }
    
    // 音频录制回调
    private val audioRecordingCallback = object : AudioRecordingService.AudioRecordingCallback {
        override fun onAudioData(audioData: FloatArray) {
            // 音频数据已经在Service中处理，这里可以做额外处理（如果需要）
        }
        
        override fun onRecordingStarted() {
            runOnUiThread {
                isRecording.set(true)
                sessionStartTime = System.currentTimeMillis()

                // 更新UI
                tvStatus.text = "🎤 正在录音中..."
                tvRecordingStatus.text = "正在录音，请说话..."
                llActions.visibility = View.GONE
                updateUI()
                startTimeUpdate()

                // 通知悬浮窗服务更新状态
                notifyFloatingWindowRecordingState(true)

                Log.i(TAG, "录音已开始")
                showToast("开始录音")
            }
        }
        
        override fun onRecordingStopped(audioFilePath: String?) {
            runOnUiThread {
                isRecording.set(false)

                // 保存录音文件路径
                currentAudioFilePath = audioFilePath
                if (audioFilePath != null) {
                    Log.i(TAG, "录音文件保存完成: $audioFilePath")
                } else {
                    Log.w(TAG, "录音文件保存失败")
                }

                // 停止时间更新
                stopTimeUpdate()

                // 通知悬浮窗服务更新状态
                notifyFloatingWindowRecordingState(false)

                // 自动优化ASR结果
                if (autoOptimize && recognitionResults.isNotEmpty()) {
                    autoOptimizeAsrContent()
                }

                // 自动保存会议记录
                if (recognitionResults.isNotEmpty()) {
                    autoSaveMeetingRecord()
                }

                // 更新UI
                tvStatus.text = "✅ 录音完成"
                tvRecordingStatus.text = "点击开始录音"
                if (recognitionResults.isNotEmpty()) {
                    llActions.visibility = View.VISIBLE
                }
                updateUI()

                Log.i(TAG, "录音已停止")
                showToast("录音已停止")
            }
        }
        
        override fun onError(error: String) {
            runOnUiThread {
                Log.e(TAG, "录音服务错误: $error")
                showToast("录音错误: $error")
                
                // 重置状态
                isRecording.set(false)
                updateUI()
            }
        }
    }

    // 文件选择器
    private val audioFilePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { handleSelectedAudioFile(it) }
    }

    // 实时预览状态管理
    private var isShowingPreview = false
    private var currentPreviewText = ""
    private var baseResultsText = ""
    private var previewTimestamp = ""
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            Log.i(TAG, "开始创建SingleModelActivity")

            // 使用优化版的布局
            // setContentView(R.layout.activity_simple_test)
            setContentView(R.layout.activity_voice_assistant)

            // 请求权限
            ActivityCompat.requestPermissions(this, permissions, REQUEST_RECORD_AUDIO_PERMISSION)

            // 初始化UI
            initViews()

            // 初始化会议记录管理器
            initMeetingRecordManager()

            // 加载设置
            loadSettings()

            // 初始化ASR引擎
            initASREngine()
            
            // 绑定音频录制服务
            bindAudioRecordingService()

            // 注册广播接收器
            registerRecordingToggleReceiver()

            // 绑定悬浮窗服务
            bindFloatingWindowService()

            // 检查是否需要自动启动悬浮窗
            checkAutoStartFloatingWindow()

            // 处理导入音频的意图
            handleImportIntent()

            Log.i(TAG, "SingleModelActivity创建成功")

        } catch (e: Exception) {
            Log.e(TAG, "Activity创建失败", e)
            showToast("创建失败: ${e.message}")

            // 如果初始化失败，退出应用
            finish()
        }
    }

    /**
     * 处理导入音频的意图
     */
    private fun handleImportIntent() {
        try {
            val action = intent.getStringExtra("action")
            if (action == "import_audio") {
                // 延迟执行，确保UI完全初始化
                Handler(Looper.getMainLooper()).postDelayed({
                    importAudioFile()
                }, 500)
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理导入意图失败", e)
        }
    }

    private fun initViews() {
        try {
            // 初始化UI组件 - 适配新的Apple风格布局
            btnRecord = findViewById(R.id.btn_record)
            btnClear = findViewById(R.id.btn_clear)
            btnSummary = findViewById(R.id.btn_summary)
            btnSettings = findViewById(R.id.btn_settings)
            btnMeetingRecords = findViewById(R.id.btn_meeting_records)
            btnImportAudio = findViewById(R.id.btn_import_audio)
            tvStatus = findViewById(R.id.tv_status)
            tvResults = findViewById(R.id.tv_results)
            tvWordCount = findViewById(R.id.tv_word_count)
            tvRecordingStatus = findViewById(R.id.tv_recording_status)
            llActions = findViewById(R.id.ll_actions)

            // 设置点击事件
            btnRecord.setOnClickListener { toggleRecording() }
            btnClear.setOnClickListener { clearResults() }
            btnSummary.setOnClickListener { generateMeetingSummary() }
            btnSettings.setOnClickListener { openSettings() }
            btnMeetingRecords.setOnClickListener { openMeetingRecords() }
            btnImportAudio.setOnClickListener { importAudioFile() }

            // 设置初始状态
            tvStatus.text = "正在初始化单模型ASR引擎..."
            tvRecordingStatus.text = "点击开始录音"
            tvResults.hint = "转录结果将在这里显示..."
            tvWordCount.text = "0 字"
            llActions.visibility = View.GONE // 初始隐藏操作按钮
            updateUI()

            Log.i(TAG, "UI初始化成功")

        } catch (e: Exception) {
            Log.e(TAG, "UI初始化失败", e)
            showToast("UI初始化失败: ${e.message}")
        }
    }

    /**
     * 初始化会议记录管理器
     */
    private fun initMeetingRecordManager() {
        try {
            meetingRecordManager = MeetingRecordManager.getInstance(this)
            audioRecordingManager = AudioRecordingManager(this)
            Log.d(TAG, "会议记录管理器初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "会议记录管理器初始化失败", e)
        }
    }

    /**
     * 加载设置
     */
    private fun loadSettings() {
        try {
            // 从LLMApiKeyManager加载自动优化设置
            autoOptimize = LLMApiKeyManager.getAutoOptimize(this)
            Log.d(TAG, "自动优化设置已加载: $autoOptimize")
        } catch (e: Exception) {
            Log.e(TAG, "加载设置失败", e)
            // 使用默认值
            autoOptimize = true
        }
    }

    /**
     * 打开设置页面
     */
    private fun openSettings() {
        try {
            val intent = android.content.Intent(this, SettingsActivity::class.java)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开设置页面失败", e)
            showToast("打开设置页面失败: ${e.message}")
        }
    }

    /**
     * 打开会议记录页面
     */
    private fun openMeetingRecords() {
        try {
            val intent = android.content.Intent(this, MeetingRecordsActivity::class.java)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "打开会议记录页面失败", e)
            showToast("打开会议记录页面失败: ${e.message}")
        }
    }

    /**
     * 导入音频文件
     */
    private fun importAudioFile() {
        try {
            // 检查是否正在录音
            if (isRecording.get()) {
                showToast("请先停止当前录音")
                return
            }

            // 显示文件格式说明对话框
            AlertDialog.Builder(this)
                .setTitle("📁 导入音频文件")
                .setMessage("支持的音频格式：\n• WAV (推荐)\n• MP3 (常用格式)\n• M4A / AAC\n\n注意：\n• 文件大小限制：100MB以内\n• 非WAV格式将自动转换\n• 转录包含完整的声纹识别功能\n• 结果将自动保存到会议记录")
                .setPositiveButton("选择音频文件") { _, _ ->
                    launchAudioFilePicker()
                }
                .setNegativeButton("取消", null)
                .show()

        } catch (e: Exception) {
            Log.e(TAG, "导入音频文件失败", e)
            showToast("导入音频文件失败: ${e.message}")
        }
    }

    /**
     * 启动音频文件选择器
     */
    private fun launchAudioFilePicker() {
        try {
            // 支持多种音频格式
            audioFilePickerLauncher.launch("audio/*")
        } catch (e: Exception) {
            Log.e(TAG, "启动文件选择器失败", e)
            showToast("启动文件选择器失败: ${e.message}")
        }
    }

    /**
     * 处理选中的音频文件
     */
    private fun handleSelectedAudioFile(uri: Uri) {
        try {
            // 获取文件信息
            val fileName = getFileName(uri)
            val fileSize = getFileSize(uri)

            Log.i(TAG, "选中音频文件: $fileName, 大小: ${fileSize / 1024 / 1024}MB")

            // 检查文件大小（限制为100MB）
            if (fileSize > 100 * 1024 * 1024) {
                showToast("文件过大，请选择小于100MB的音频文件")
                return
            }

            // 显示确认对话框
            AlertDialog.Builder(this)
                .setTitle("确认转录")
                .setMessage("文件名: $fileName\n大小: ${String.format("%.1f", fileSize / 1024.0 / 1024.0)} MB\n\n确定要转录这个音频文件吗？")
                .setPositiveButton("开始转录") { _, _ ->
                    startAudioFileTranscription(uri, fileName)
                }
                .setNegativeButton("取消", null)
                .show()

        } catch (e: Exception) {
            Log.e(TAG, "处理音频文件失败", e)
            showToast("处理音频文件失败: ${e.message}")
        }
    }

    /**
     * 初始化单模型ASR引擎
     */
    private fun initASREngine() {
        tvStatus.text = "正在初始化单模型ASR引擎..."

        Thread {
            try {
                // 初始化ASR引擎，传入Context以支持声纹持久化
                asrEngine = SingleModelASREngine(assets, SAMPLE_RATE, context = this@SingleModelActivity)
                asrEngine.setListener(this@SingleModelActivity)

                val success = asrEngine.initialize()

                runOnUiThread {
                    if (success) {
                        isInitialized = true
                        tvStatus.text = "✅ 单模型ASR引擎初始化成功 - 准备就绪"
                        Log.i(TAG, "单模型ASR引擎初始化成功")

                        // 显示恢复的声纹数量
                        val speakerCount = asrEngine.getSpeakerCount()
                        if (speakerCount > 0) {
                            showToast("已恢复 $speakerCount 个声纹")
                            Log.i(TAG, "已恢复 $speakerCount 个声纹")
                        }
                    } else {
                        tvStatus.text = "❌ 单模型ASR引擎初始化失败"
                        showToast("初始化失败")
                        Log.e(TAG, "单模型ASR引擎初始化失败")
                    }
                    updateUI()
                }

            } catch (e: Exception) {
                Log.e(TAG, "单模型ASR引擎初始化异常", e)
                runOnUiThread {
                    tvStatus.text = "❌ 单模型ASR引擎初始化异常: ${e.message}"
                    showToast("初始化失败: ${e.message}")
                    updateUI()
                }
            }
        }.start()
    }
    
    /**
     * 切换录音状态
     */
    private fun toggleRecording() {
        if (!isInitialized) {
            showToast("单模型ASR引擎未初始化")
            return
        }

        if (!isRecording.get()) {
            startRecording()
        } else {
            stopRecording()
        }
    }
    
    /**
     * 开始录音
     */
    private fun startRecording() {
        // 检查权限
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            showToast("需要录音权限")
            ActivityCompat.requestPermissions(this, permissions, REQUEST_RECORD_AUDIO_PERMISSION)
            return
        }
        
        // 检查Service是否已绑定
        if (!isServiceBound || audioRecordingService == null) {
            Log.e(TAG, "音频录制服务未绑定")
            showToast("音频录制服务未就绪，请稍后重试")
            return
        }
        
        try {
            // 重置ASR引擎状态
            asrEngine.reset()
            
            // 生成会议ID
            val tempMeetingId = UUID.randomUUID().toString().substring(0, 8)
            
            // 通过Service开始录音
            val success = audioRecordingService!!.startRecording(tempMeetingId)
            if (!success) {
                Log.e(TAG, "启动录音服务失败")
                showToast("启动录音失败")
                return
            }
            
            Log.i(TAG, "开始录音 - 单模型模式")
            
        } catch (e: Exception) {
            Log.e(TAG, "开始录音失败", e)
            showToast("录音失败: ${e.message}")
        }
    }
    
    /**
     * 停止录音
     */
    private fun stopRecording() {
        // 检查Service是否已绑定
        if (!isServiceBound || audioRecordingService == null) {
            Log.w(TAG, "音频录制服务未绑定，无法停止录音")
            return
        }
        
        try {
            // 通过Service停止录音
            val finalAudioFilePath = audioRecordingService!!.stopRecording()
            if (finalAudioFilePath != null) {
                currentAudioFilePath = finalAudioFilePath
                Log.i(TAG, "录音文件保存完成: $finalAudioFilePath")
            } else {
                Log.w(TAG, "录音文件保存失败")
                currentAudioFilePath = null
            }
            
            Log.i(TAG, "录音已停止")
            
        } catch (e: Exception) {
            Log.e(TAG, "停止录音失败", e)
        }
    }

    /**
     * 绑定音频录制服务
     */
    private fun bindAudioRecordingService() {
        try {
            val intent = Intent(this, AudioRecordingService::class.java)
            val success = bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
            if (success) {
                Log.i(TAG, "正在绑定AudioRecordingService")
            } else {
                Log.e(TAG, "绑定AudioRecordingService失败")
                showToast("音频录制服务绑定失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "绑定AudioRecordingService异常", e)
            showToast("音频录制服务绑定异常: ${e.message}")
        }
    }
    
    /**
     * 解绑音频录制服务
     */
    private fun unbindAudioRecordingService() {
        try {
            if (isServiceBound) {
                // 停止录音（如果正在录音）
                if (audioRecordingService?.isRecording() == true) {
                    audioRecordingService?.stopRecording()
                }
                
                // 清除回调
                audioRecordingService?.setCallback(null)
                
                // 解绑服务
                unbindService(serviceConnection)
                isServiceBound = false
                audioRecordingService = null
                
                Log.i(TAG, "AudioRecordingService已解绑")
            }
        } catch (e: Exception) {
            Log.e(TAG, "解绑AudioRecordingService异常", e)
        }
    }
    
    /**
     * 处理ASR识别结果 - 支持说话人识别
     */
    private fun handleASRResult(result: SingleModelASREngine.ASRResult) {
        Log.d(TAG, "收到ASR结果 - 类型: ${result.type}, 文本: '${result.text}', 时间戳: ${result.timestamp}")

        when (result.type) {
            SingleModelASREngine.ResultType.PREVIEW -> {
                // 显示实时预览
                showPreview(result.text)
                // Log.i(TAG, "处理预览结果: '${result.text}'")
            }
            SingleModelASREngine.ResultType.FINAL, SingleModelASREngine.ResultType.ENDPOINT -> {
                // 添加带说话人信息的最终结果
                addFinalResultWithSpeaker(result)
                // Log.i(TAG, "处理最终结果: '${result.text}'")
            }
        }
    }

    /**
     * 显示实时预览 - 直接更新主结果区域的当前行（累积显示）
     */
    private fun showPreview(text: String) {
        if (text.isBlank()) {
            // Log.d(TAG, "showPreview: 收到空白文本，跳过处理")
            return
        }

        // Log.d(TAG, "showPreview: 收到文本='$text', 当前预览状态=$isShowingPreview, 当前累积文本='$currentPreviewText'")

        // 如果不是正在显示预览，开始新的预览（生成新时间戳）
        if (!isShowingPreview) {
            previewTimestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
            currentPreviewText = text  // 开始新预览，直接设置文本
            isShowingPreview = true
            // Log.d(TAG, "showPreview: 开始新预览 - 时间戳=$previewTimestamp, 初始文本='$currentPreviewText'")
        } else {
            // 正在显示预览，累积文本内容（ASR引擎返回的是增量文本）
            currentPreviewText += text  // 累积增量文本
            // Log.d(TAG, "showPreview: 累积预览文本 - 增量='$text', 累积后='$currentPreviewText'")
        }

        // 使用固定的时间戳构建预览行
        val previewLine = "[$previewTimestamp] $currentPreviewText"

        // 更新主结果区域：基础内容 + 当前累积预览行
        val displayText = if (baseResultsText.isNotEmpty()) {
            "$baseResultsText$previewLine"
        } else {
            previewLine
        }

        // Log.d(TAG, "showPreview: 更新显示文本='$displayText'")
        tvResults.text = displayText
    }

     /**
     * 添加最终结果 - 覆盖预览文本或添加新行，支持说话人信息
     */
    private fun addFinalResult(text: String) {
        if (text.isNotBlank()) {
            val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())
            val formattedResult = "[$timestamp] $text\n"

            Log.d(TAG, "addFinalResult: 收到最终结果='$text', 当前预览状态=$isShowingPreview, 当前预览文本='$currentPreviewText'")

            // 如果当前正在显示预览，则覆盖预览行
            if (isShowingPreview) {
                // 直接替换最后的预览内容
                recognitionResults.append(formattedResult)
                isShowingPreview = false
                currentPreviewText = ""
                previewTimestamp = ""
                Log.d(TAG, "addFinalResult: 覆盖预览内容，重置预览状态")
            } else {
                // 正常添加新行
                recognitionResults.append(formattedResult)
                Log.d(TAG, "addFinalResult: 正常添加新行")
            }

            // 更新基础结果文本
            baseResultsText = recognitionResults.toString()
            tvResults.text = baseResultsText

            Log.d(TAG, "addFinalResult: 更新基础结果文本，长度=${baseResultsText.length}")

            // 更新统计
            wordCount += text.length
            recognitionCount++
            updateStatistics()
        }
    }

    /**
     * 添加带说话人信息的最终结果
     */
    private fun addFinalResultWithSpeaker(result: SingleModelASREngine.ASRResult) {
        if (result.text.isNotBlank()) {
            val timestamp = SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())

            // 构建结果文本
            val formattedResult = "[$timestamp]-${result.speakerName}: ${result.text}\n"

            // Log.d(TAG, "addFinalResultWithSpeaker: 收到最终结果='${result.text}', 当前预览状态=$isShowingPreview")

            // 如果当前正在显示预览，则覆盖预览行
            if (isShowingPreview) {
                // 直接替换最后的预览内容
                recognitionResults.append(formattedResult)
                isShowingPreview = false
                currentPreviewText = ""
                previewTimestamp = ""
                // Log.d(TAG, "addFinalResultWithSpeaker: 覆盖预览内容，重置预览状态")
            } else {
                // 正常添加新行
                recognitionResults.append(formattedResult)
                // Log.d(TAG, "addFinalResultWithSpeaker: 正常添加新行")
            }

            // 更新基础结果文本
            baseResultsText = recognitionResults.toString()
            tvResults.text = baseResultsText

            // Log.d(TAG, "addFinalResultWithSpeaker: 更新基础结果文本，长度=${baseResultsText.length}")

            // 更新统计
            wordCount += result.text.length
            recognitionCount++
            updateStatistics()
        }
    }
    
    /**
     * 清空结果 - 适配新的UI布局
     */
    private fun clearResults() {
        Log.d(TAG, "clearResults: 清空所有结果和预览状态")

        recognitionResults.clear()
        baseResultsText = ""
        isShowingPreview = false
        currentPreviewText = ""
        previewTimestamp = ""
        currentMeetingRecordId = null // 重置会议记录ID
        currentAudioFilePath = null // 重置录音文件路径

        tvResults.text = ""
        tvResults.hint = "转录结果将在这里显示..."
        wordCount = 0
        recognitionCount = 0
        llActions.visibility = View.GONE // 隐藏操作按钮
        updateStatistics()
        showToast("结果已清空")

        Log.d(TAG, "clearResults: 清空完成")
    }

    /**
     * 复制结果到剪贴板
     */
    private fun copyResults() {
        if (recognitionResults.isEmpty()) {
            showToast("没有可复制的内容")
            return
        }

        try {
            // 构建要复制的文本内容
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
            val content = buildString {
                append("语音识别结果\n")
                append("生成时间: $timestamp\n")
                append("总字数: $wordCount\n")
                append("识别次数: $recognitionCount\n")
                append("======================================\n\n")
                append(recognitionResults.toString())
            }

            // 复制到剪贴板
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("ASR识别结果", content)
            clipboard.setPrimaryClip(clip)

            showToast("识别结果已复制到剪贴板")

        } catch (e: Exception) {
            Log.e(TAG, "复制结果失败", e)
            showToast("复制失败: ${e.message}")
        }
    }
    
    /**
     * 更新UI状态 - 适配新的Apple风格布局
     */
    private fun updateUI() {
        btnRecord.isEnabled = isInitialized

        // 更新录音按钮状态和样式
        if (isRecording.get()) {
            btnRecord.text = "停止录音"
            btnRecord.setBackgroundResource(R.drawable.record_button_recording)
        } else {
            btnRecord.text = "开始录音"
            btnRecord.setBackgroundResource(R.drawable.record_button_idle)
        }

        // 更新操作按钮状态
        btnClear.isEnabled = !isRecording.get() && recognitionResults.isNotEmpty()
        btnSummary.isEnabled = !isRecording.get() && recognitionResults.isNotEmpty()
        btnSettings.isEnabled = !isRecording.get()

        // 更新操作按钮区域可见性
        llActions.visibility = if (!isRecording.get() && recognitionResults.isNotEmpty()) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }

    /**
     * 更新统计信息 - 适配新的UI布局
     */
    private fun updateStatistics() {
        // 更新字数统计
        tvWordCount.text = "$wordCount 字"

        // 可以在状态栏显示更详细的统计信息
        val elapsed = if (sessionStartTime > 0) {
            (System.currentTimeMillis() - sessionStartTime) / 1000
        } else 0

        val minutes = elapsed / 60
        val seconds = elapsed % 60

        // 在录音时显示时长，停止后显示总结信息
        if (isRecording.get()) {
            tvStatus.text = "🎤 录音中 ${String.format("%02d:%02d", minutes, seconds)}"
        } else if (recognitionResults.isNotEmpty()) {
            tvStatus.text = "✅ 录音完成 - 共 $recognitionCount 次识别"
        }
    }

    /**
     * 开始时间更新
     */
    private fun startTimeUpdate() {
        timeUpdateRunnable = object : Runnable {
            override fun run() {
                if (isRecording.get()) {
                    updateStatistics()
                    timeHandler.postDelayed(this, 1000)
                }
            }
        }
        timeHandler.post(timeUpdateRunnable!!)
    }

    /**
     * 停止时间更新
     */
    private fun stopTimeUpdate() {
        timeUpdateRunnable?.let { timeHandler.removeCallbacks(it) }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    // ASRListener接口实现
    override fun onResult(result: SingleModelASREngine.ASRResult) {
        runOnUiThread {
            handleASRResult(result)
        }
    }

    override fun onError(error: String) {
        runOnUiThread {
            Log.e(TAG, "ASR错误: $error")
            tvStatus.text = "❌ 识别错误: $error"
            showToast("识别错误: $error")
        }
    }

    override fun onStatusChanged(status: String) {
        runOnUiThread {
            tvStatus.text = status
        }
    }

    override fun onSpeakerIdentified(speakerInfo: SingleModelASREngine.SpeakerInfo) {
        runOnUiThread {
            Log.d(TAG, "说话人识别结果: ${speakerInfo.name} (置信度: ${speakerInfo.confidence})")
            // 说话人识别结果已经在ASRResult中处理，这里只记录日志
        }
    }

    override fun onSpeakerRegistered(speakerName: String, success: Boolean) {
        runOnUiThread {
            val message = if (success) {
                "说话人 '$speakerName' 注册成功"
            } else {
                "说话人 '$speakerName' 注册失败"
            }
            Log.i(TAG, message)
            showToast(message)
        }
    }

    override fun onSpeakerRemoved(speakerName: String, success: Boolean) {
        runOnUiThread {
            val message = if (success) {
                "说话人 '$speakerName' 删除成功"
            } else {
                "说话人 '$speakerName' 删除失败"
            }
            Log.i(TAG, message)
            showToast(message)
        }
    }

    override fun onVadStatusChanged(isSpeech: Boolean) {
        runOnUiThread {
            Log.d(TAG, "VAD状态变化: ${if (isSpeech) "检测到语音" else "静音"}")
            // VAD状态变化，可以用于UI指示，暂时只记录日志
        }
    }

    override fun onResume() {
        super.onResume()
        // 重新加载设置，以便从设置页面返回时更新配置
        loadSettings()
        
        // 如果Service已绑定但ASR引擎未设置，重新设置
        if (isServiceBound && audioRecordingService != null && isInitialized) {
            audioRecordingService?.setAsrEngine(asrEngine)
            audioRecordingService?.setAudioRecordingManager(audioRecordingManager)
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        
        // 解绑音频录制服务
        unbindAudioRecordingService()
        
        // 释放ASR引擎
        if (isInitialized) {
            try {
                asrEngine.release()
            } catch (e: Exception) {
                Log.e(TAG, "释放ASR引擎失败", e)
            }
        }
        
        // 停止时间更新
        stopTimeUpdate()
        
        Log.i(TAG, "Activity已销毁")
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.i(TAG, "录音权限已授予")
                showToast("录音权限已授予")
            } else {
                Log.w(TAG, "录音权限被拒绝")
                showToast("录音权限被拒绝，部分功能可能无法使用")
            }
        }
    }

    // ==================== 已移除的功能 ====================
    // 声纹管理功能已移至SettingsActivity





    // ==================== AI 功能 ====================
    // ASR优化功能已移至SettingsActivity，这里保留会议总结功能

    /**
     * 自动优化ASR内容 - 停止录音后自动执行
     */
    private fun autoOptimizeAsrContent() {
        // 检查当前LLM是否可用，如果没有配置则静默跳过
        if (!LLMManager.isCurrentLLMAvailable(this)) {
            Log.d(TAG, "当前LLM未配置，跳过自动优化")
            return
        }

        val originalContent = recognitionResults.toString()
        if (originalContent.trim().isEmpty()) {
            Log.d(TAG, "识别结果为空，跳过自动优化")
            return
        }

        // 显示优化提示
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)
        showToast("🔄 正在使用${currentProvider.displayName}自动优化ASR结果...")
        Log.i(TAG, "开始使用${currentProvider.displayName}自动优化ASR结果")

        // 后台执行优化
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.optimizeAsrContent(this@SingleModelActivity, originalContent)

                runOnUiThread {
                    if (result.success) {
                        // 直接替换结果
                        recognitionResults.clear()
                        recognitionResults.append(result.content)
                        baseResultsText = recognitionResults.toString()
                        tvResults.text = baseResultsText

                        // 更新统计
                        wordCount = result.content.length
                        updateStatistics()

                        // 更新已保存的会议记录中的优化内容
                        updateMeetingRecordOptimizedContent(result.content)

                        showToast("✅ ASR结果已自动优化完成")
                        Log.i(TAG, "自动优化ASR结果成功")
                    } else {
                        Log.w(TAG, "自动优化失败: ${result.error}")
                        showToast("⚠️ 自动优化失败，保持原始结果")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    Log.w(TAG, "自动优化异常，保持原始结果", e)
                    showToast("⚠️ 自动优化异常，保持原始结果")
                }
            }
        }
    }

    // ASR优化相关方法已移至LLMManager

    // ASR优化对话框相关方法已移至LLMManager

    /**
     * 生成会议总结
     */
    private fun generateMeetingSummary() {
        if (recognitionResults.isEmpty()) {
            showToast("没有语音识别内容可以总结")
            return
        }

        // 检查当前LLM是否可用
        if (!LLMManager.isCurrentLLMAvailable(this)) {
            showLLMConfigDialog()
            return
        }

        // 获取当前的语音转文字内容
        val speechContent = recognitionResults.toString()

        if (speechContent.trim().isEmpty()) {
            showToast("语音识别内容为空")
            return
        }

        // 获取当前LLM提供商信息
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        // 显示加载对话框
        val loadingDialog = AlertDialog.Builder(this)
            .setTitle("AI会议总结")
            .setMessage("正在使用 ${currentProvider.displayName} 生成会议总结，请稍候...")
            .setCancelable(false)
            .create()
        loadingDialog.show()

        // 在后台线程调用LLM API
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.generateMeetingSummary(this@SingleModelActivity, speechContent)

                runOnUiThread {
                    loadingDialog.dismiss()
                    if (result.success) {
                        // 更新已保存的会议记录中的总结内容
                        updateMeetingRecordSummaryContent(result.content)
                        showMeetingSummaryDialog(result.content, speechContent)
                    } else {
                        Log.e(TAG, "生成会议总结失败: ${result.error}")
                        showToast("生成会议总结失败: ${result.error}")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    loadingDialog.dismiss()
                    Log.e(TAG, "生成会议总结异常", e)
                    showToast("生成会议总结异常: ${e.message}")
                }
            }
        }
    }

    // Gemini API调用已移至LLMManager

    /**
     * 显示会议总结对话框
     */
    private fun showMeetingSummaryDialog(summary: String, originalContent: String) {
        val dialog = AlertDialog.Builder(this)
            .setTitle("🤖 AI会议总结")
            .create()

        // 创建自定义布局
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        // 总结内容显示区域
        val summaryText = android.widget.TextView(this).apply {
            text = summary
            textSize = 14f
            setPadding(20, 20, 20, 20)
            setBackgroundResource(android.R.drawable.edit_text)
            isVerticalScrollBarEnabled = true
            maxLines = 15
        }

        // 按钮区域
        val buttonLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(0, 20, 0, 0)
        }

        val copySummaryBtn = android.widget.Button(this).apply {
            text = "复制总结"
            layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(0, 0, 10, 0)
            }
        }

        val copyOriginalBtn = android.widget.Button(this).apply {
            text = "复制原文"
            layoutParams = android.widget.LinearLayout.LayoutParams(0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(10, 0, 0, 0)
            }
        }

        buttonLayout.addView(copySummaryBtn)
        buttonLayout.addView(copyOriginalBtn)

        layout.addView(summaryText)
        layout.addView(buttonLayout)

        dialog.setView(layout)

        // 设置按钮点击事件
        copySummaryBtn.setOnClickListener {
            copyToClipboard("会议总结", summary)
            showToast("会议总结已复制到剪贴板")
        }

        copyOriginalBtn.setOnClickListener {
            copyToClipboard("会议原文", originalContent)
            showToast("会议原文已复制到剪贴板")
        }

        dialog.setButton(AlertDialog.BUTTON_NEGATIVE, "关闭") { _, _ ->
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * 复制文本到剪贴板
     */
    private fun copyToClipboard(label: String, text: String) {
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText(label, text)
        clipboard.setPrimaryClip(clip)
    }

    /**
     * 显示LLM配置对话框
     */
    private fun showLLMConfigDialog() {
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        AlertDialog.Builder(this)
            .setTitle("LLM 配置")
            .setMessage("当前选择的 ${currentProvider.displayName} 未配置API密钥。\n\n请前往设置页面配置LLM提供商和API密钥。")
            .setPositiveButton("打开设置") { _, _ ->
                openSettings()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    // API密钥配置对话框已移至SettingsActivity

    /**
     * 自动保存会议记录
     */
    private fun autoSaveMeetingRecord() {
        try {
            val originalContent = recognitionResults.toString()
            if (originalContent.trim().isEmpty()) {
                Log.d(TAG, "识别结果为空，跳过自动保存")
                return
            }

            // 计算录音时长
            val duration = if (sessionStartTime > 0) {
                (System.currentTimeMillis() - sessionStartTime) / 1000
            } else 0

            // 获取说话人数量（从ASR引擎）
            val speakerCount = try {
                asrEngine.getSpeakerCount()
            } catch (e: Exception) {
                0
            }

            // 创建会议记录
            val meetingRecord = meetingRecordManager.createMeetingRecord(
                originalContent = originalContent,
                wordCount = wordCount,
                duration = duration,
                speakerCount = speakerCount,
                audioFilePath = currentAudioFilePath ?: ""
            )

            // 保存记录
            val success = meetingRecordManager.saveMeetingRecord(meetingRecord)
            if (success) {
                // 保存当前会议记录ID，用于后续更新优化内容和总结
                currentMeetingRecordId = meetingRecord.id
                Log.i(TAG, "会议记录自动保存成功: ${meetingRecord.title}")
                
                // 异步生成LLM标题
                generateLLMTitleForRecord(meetingRecord.id, originalContent)
                
                // 可以显示一个轻微的提示，但不要打断用户
                // showToast("📝 会议记录已自动保存")
            } else {
                Log.w(TAG, "会议记录自动保存失败")
            }

        } catch (e: Exception) {
            Log.e(TAG, "自动保存会议记录异常", e)
        }
    }

    /**
     * 开始音频文件转录
     */
    private fun startAudioFileTranscription(uri: Uri, fileName: String) {
        try {
            // 清空当前结果
            clearResults()

            // 显示进度对话框
            val progressDialog = AlertDialog.Builder(this)
                .setTitle("🎵 转录音频文件")
                .setMessage("正在处理「$fileName」...\n\n🔄 准备转换音频格式...\n\n✨ 转录将包含完整的声纹识别功能")
                .setCancelable(false)
                .setNegativeButton("取消") { _, _ ->
                    // TODO: 实现取消转录
                }
                .create()
            progressDialog.show()

            // 更新UI状态
            tvStatus.text = "🎵 正在转录音频文件..."
            tvRecordingStatus.text = "正在处理「$fileName」"
            btnImportAudio.isEnabled = false

            // 复制文件到临时目录并转换为WAV格式
            CoroutineScope(Dispatchers.IO).launch {
                var tempWavFile: File? = null
                try {
                    // 更新进度：开始转换
                    runOnUiThread {
                        progressDialog.setMessage("正在处理「$fileName」...\n\n🔄 正在转换音频格式...\n\n请稍候，这可能需要一些时间")
                    }

                    tempWavFile = copyAndConvertToWav(uri, fileName)
                    if (tempWavFile == null) {
                        runOnUiThread {
                            progressDialog.dismiss()
                            showConversionFailedDialog(fileName)
                            resetUIAfterTranscription()
                        }
                        return@launch
                    }

                    // 更新进度：转换完成，开始转录
                    runOnUiThread {
                        progressDialog.setMessage("正在处理「$fileName」...\n\n✅ 格式转换完成\n🎙️ 开始语音转录...")
                    }

                    // 复制原始文件到永久存储位置
                    val permanentAudioFile = copyOriginalAudioFile(uri, fileName)

                    // 开始转录
                    val transcriber = AudioFileTranscriber(this@SingleModelActivity)
                    transcriber.transcribeAudioFile(
                        audioFilePath = tempWavFile.absolutePath,
                        callback = object : AudioFileTranscriber.TranscriptionCallback {
                            override fun onProgress(progress: Int, currentText: String) {
                                runOnUiThread {
                                    val displayText = currentText.take(100)
                                    val hasSpeaker = currentText.contains(":")
                                    val speakerInfo = if (hasSpeaker) "✅ 检测到说话人信息" else "🔍 正在识别说话人..."
                                    progressDialog.setMessage("正在处理「$fileName」...\n\n进度: $progress%\n$speakerInfo\n\n当前识别: ${displayText}${if (currentText.length > 100) "..." else ""}")

                                    // 实时更新UI
                                    recognitionResults.clear()
                                    recognitionResults.append(currentText)
                                    tvResults.text = currentText
                                    wordCount = currentText.length
                                    updateStatistics()
                                }
                            }

                            override fun onComplete(finalText: String) {
                                runOnUiThread {
                                    progressDialog.dismiss()
                                    handleTranscriptionComplete(finalText, tempWavFile, permanentAudioFile, fileName)
                                }
                            }

                            override fun onError(error: String) {
                                runOnUiThread {
                                    progressDialog.dismiss()
                                    Log.e(TAG, "音频文件转录失败: $error")
                                    showToast("转录失败: $error")
                                    // 清理临时文件
                                    cleanupTempFile(tempWavFile)
                                    resetUIAfterTranscription()
                                }
                            }
                        }
                    )

                } catch (e: Exception) {
                    runOnUiThread {
                        progressDialog.dismiss()
                        Log.e(TAG, "音频文件转录异常", e)
                        showToast("转录异常: ${e.message}")
                        // 清理临时文件
                        cleanupTempFile(tempWavFile)
                        resetUIAfterTranscription()
                    }
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "开始音频文件转录失败", e)
            showToast("开始转录失败: ${e.message}")
        }
    }

    /**
     * 更新会议记录的优化内容
     */
    private fun updateMeetingRecordOptimizedContent(optimizedContent: String) {
        val recordId = currentMeetingRecordId ?: return

        try {
            val existingRecord = meetingRecordManager.getRecordById(recordId) ?: return
            val updatedRecord = existingRecord.copy(
                optimizedContent = optimizedContent,
                wordCount = optimizedContent.length
            )

            val success = meetingRecordManager.saveMeetingRecord(updatedRecord)
            if (success) {
                Log.d(TAG, "会议记录优化内容已更新")
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新会议记录优化内容失败", e)
        }
    }

    /**
     * 更新会议记录的总结内容
     */
    private fun updateMeetingRecordSummaryContent(summaryContent: String) {
        val recordId = currentMeetingRecordId ?: return

        try {
            val existingRecord = meetingRecordManager.getRecordById(recordId) ?: return
            val updatedRecord = existingRecord.copy(summaryContent = summaryContent)

            val success = meetingRecordManager.saveMeetingRecord(updatedRecord)
            if (success) {
                Log.d(TAG, "会议记录总结内容已更新")
            }
        } catch (e: Exception) {
            Log.e(TAG, "更新会议记录总结内容失败", e)
        }
    }

    /**
     * 处理转录完成
     */
    private fun handleTranscriptionComplete(finalText: String, tempWavFile: File, permanentAudioFile: String?, originalFileName: String) {
        try {
            if (finalText.trim().isEmpty()) {
                showToast("转录结果为空")
                cleanupTempFile(tempWavFile)
                resetUIAfterTranscription()
                return
            }

            // 更新UI
            recognitionResults.clear()
            recognitionResults.append(finalText)
            tvResults.text = finalText
            wordCount = finalText.length
            updateStatistics()
            llActions.visibility = View.VISIBLE
            updateUI() // 刷新按钮启用状态

            // 计算文件时长（简单估算）
            val fileSizeBytes = tempWavFile.length()
            val estimatedDuration = (fileSizeBytes / (16000 * 2)).toLong() // 16kHz, 16bit

            // 创建会议记录（使用永久音频文件路径）
            val meetingRecord = meetingRecordManager.createMeetingRecord(
                originalContent = finalText,
                wordCount = wordCount,
                duration = estimatedDuration,
                speakerCount = countSpeakers(finalText),
                audioFilePath = permanentAudioFile ?: ""
            )

            // 保存记录
            val success = meetingRecordManager.saveMeetingRecord(meetingRecord)
            if (success) {
                currentMeetingRecordId = meetingRecord.id
                Log.i(TAG, "导入音频文件转录完成并保存: ${meetingRecord.title}")
                showToast("✅ 转录完成！已保存到会议记录")
            } else {
                Log.w(TAG, "转录完成但保存失败")
                showToast("✅ 转录完成！但保存失败")
            }

            // 更新UI状态
            tvStatus.text = "✅ 音频文件转录完成"
            tvRecordingStatus.text = "转录完成：$originalFileName"

        } catch (e: Exception) {
            Log.e(TAG, "处理转录完成异常", e)
            showToast("处理转录结果失败: ${e.message}")
        } finally {
            // 清理临时WAV文件
            cleanupTempFile(tempWavFile)
            resetUIAfterTranscription()
        }
    }

    /**
     * 显示转换失败对话框
     */
    private fun showConversionFailedDialog(fileName: String) {
        AlertDialog.Builder(this)
            .setTitle("⚠️ 音频转换失败")
            .setMessage("文件「$fileName」转换失败，可能的原因：\n\n" +
                    "• 文件格式不支持或已损坏\n" +
                    "• 文件过大导致内存不足\n" +
                    "• 设备存储空间不足\n\n" +
                    "建议：\n" +
                    "• 尝试使用WAV格式文件\n" +
                    "• 选择较小的音频文件\n" +
                    "• 关闭其他应用释放内存")
            .setPositiveButton("重新选择") { _, _ ->
                importAudioFile()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 重置转录后的UI状态
     */
    private fun resetUIAfterTranscription() {
        btnImportAudio.isEnabled = true
        updateUI() // 确保UI状态正确更新
    }

    /**
     * 统计说话人数量
     */
    private fun countSpeakers(text: String): Int {
        return try {
            val speakers = mutableSetOf<String>()
            text.split("\n").forEach { line ->
                val colonIndex = line.indexOf(":")
                if (colonIndex > 0) {
                    val speaker = line.substring(0, colonIndex).trim()
                    if (speaker.isNotEmpty()) {
                        speakers.add(speaker)
                    }
                }
            }
            speakers.size
        } catch (e: Exception) {
            0
        }
    }

    /**
     * 获取文件名
     */
    private fun getFileName(uri: Uri): String {
        return try {
            contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                cursor.moveToFirst()
                cursor.getString(nameIndex)
            } ?: "未知文件"
        } catch (e: Exception) {
            "未知文件"
        }
    }

    /**
     * 获取文件大小
     */
    private fun getFileSize(uri: Uri): Long {
        return try {
            contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                val sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)
                cursor.moveToFirst()
                cursor.getLong(sizeIndex)
            } ?: 0L
        } catch (e: Exception) {
            0L
        }
    }

    /**
     * 复制并转换音频文件为WAV格式
     */
    private fun copyAndConvertToWav(uri: Uri, fileName: String): File? {
        return try {
            // 创建临时文件
            val tempDir = File(cacheDir, "imported_audio")
            if (!tempDir.exists()) {
                tempDir.mkdirs()
            }

            val tempFile = File(tempDir, "converted_${System.currentTimeMillis()}.wav")

            // 使用音频格式转换器
            val converter = AudioFormatConverter(this)
            val success = converter.convertToWav(uri, tempFile)

            if (success && tempFile.exists()) {
                Log.i(TAG, "音频文件转换完成: ${tempFile.absolutePath}")
                return tempFile
            } else {
                Log.w(TAG, "音频文件转换失败: $fileName")
                return null
            }

        } catch (e: Exception) {
            Log.e(TAG, "音频文件转换异常", e)
            null
        }
    }

    /**
     * 复制原始音频文件到永久存储位置
     */
    private fun copyOriginalAudioFile(uri: Uri, fileName: String): String? {
        return try {
            // 创建录音目录
            val recordingsDir = File(filesDir, "recordings")
            if (!recordingsDir.exists()) {
                recordingsDir.mkdirs()
            }

            // 生成永久文件名
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val extension = fileName.substringAfterLast(".", "")
            val permanentFileName = "imported_${timestamp}.${extension}"
            val permanentFile = File(recordingsDir, permanentFileName)

            // 复制文件
            contentResolver.openInputStream(uri)?.use { inputStream ->
                FileOutputStream(permanentFile).use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }

            if (permanentFile.exists()) {
                Log.i(TAG, "原始音频文件已保存: ${permanentFile.absolutePath}")
                return permanentFile.absolutePath
            } else {
                Log.w(TAG, "原始音频文件保存失败")
                return null
            }

        } catch (e: Exception) {
            Log.e(TAG, "复制原始音频文件异常", e)
            null
        }
    }

    /**
     * 清理临时文件
     */
    private fun cleanupTempFile(tempFile: File?) {
        try {
            if (tempFile != null && tempFile.exists()) {
                val deleted = tempFile.delete()
                if (deleted) {
                    Log.i(TAG, "临时文件已清理: ${tempFile.absolutePath}")
                } else {
                    Log.w(TAG, "临时文件清理失败: ${tempFile.absolutePath}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "清理临时文件异常", e)
        }
    }

    // override fun onRequestPermissionsResult(
    //     requestCode: Int,
    //     permissions: Array<String>,
    //     grantResults: IntArray
    // ) {
    //     super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    //
    //     if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
    //         if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
    //             Log.i(TAG, "录音权限已授予")
    //             showToast("录音权限已授予")
    //         } else {
    //             Log.w(TAG, "录音权限被拒绝")
    //             showToast("录音权限被拒绝，部分功能可能无法使用")
    //         }
    //     }
    // }
    
    /**
     * 为会议记录生成LLM标题
     */
    private fun generateLLMTitleForRecord(recordId: String, meetingContent: String) {
        // 检查LLM是否可用
        if (!LLMManager.isCurrentLLMAvailable(this)) {
            Log.d(TAG, "LLM不可用，跳过标题生成")
            return
        }
        
        lifecycleScope.launch {
            try {
                Log.d(TAG, "开始为会议记录生成LLM标题")
                val result = LLMManager.generateMeetingTitle(this@SingleModelActivity, meetingContent)
                
                if (result.success && result.content.isNotBlank()) {
                    // 清理生成的标题（移除可能的前缀、后缀等）
                    val cleanTitle = result.content.trim()
                        .removePrefix("标题：")
                        .removePrefix("会议标题：")
                        .removeSuffix("。")
                        .trim()
                    
                    if (cleanTitle.isNotBlank()) {
                        // 更新会议记录标题
                        meetingRecordManager.updateRecordTitle(recordId, cleanTitle)
                        Log.i(TAG, "LLM标题生成成功: $cleanTitle")
                    }
                } else {
                    Log.w(TAG, "LLM标题生成失败: ${result.error}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "生成LLM标题异常", e)
            }
        }
    }
}
