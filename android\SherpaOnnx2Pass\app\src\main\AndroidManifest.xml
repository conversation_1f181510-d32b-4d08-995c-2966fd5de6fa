<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <!-- 悬浮窗权限 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SherpaOnnx2Pass"
        tools:targetApi="31">
        <!-- Legacy Single Model Activity (for compatibility) -->
        <activity
            android:name=".SingleModelActivity"
            android:exported="true"
            android:label="高效语音识别"
            android:theme="@style/Theme.SherpaOnnx2Pass.Optimized">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- LLM设置页面 -->
        <activity
            android:name=".SettingsActivity"
            android:exported="false"
            android:label="LLM 设置"
            android:parentActivityName=".SingleModelActivity"
            android:theme="@style/Theme.SherpaOnnx2Pass">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".SingleModelActivity" />
        </activity>

        <!-- 会议记录列表页面 -->
        <activity
            android:name=".MeetingRecordsActivity"
            android:exported="false"
            android:label="会议记录"
            android:parentActivityName=".SingleModelActivity"
            android:theme="@style/Theme.SherpaOnnx2Pass">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".SingleModelActivity" />
        </activity>

        <!-- 会议详情页面 -->
        <activity
            android:name=".MeetingDetailActivity"
            android:exported="false"
            android:label="会议详情"
            android:parentActivityName=".MeetingRecordsActivity"
            android:theme="@style/Theme.SherpaOnnx2Pass">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".MeetingRecordsActivity" />
        </activity>

        <!-- AI聊天页面 -->
        <activity
            android:name=".AiChatActivity"
            android:exported="false"
            android:label="AI智能问答"
            android:parentActivityName=".MeetingDetailActivity"
            android:theme="@style/Theme.SherpaOnnx2Pass">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".MeetingDetailActivity" />
        </activity>

        <!-- 音频录制前台服务 -->
        <service
            android:name=".AudioRecordingService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="microphone" />

        <!-- 悬浮窗服务 -->
        <service
            android:name=".FloatingWindowService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" >
            <property android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="floating_window" />
        </service>
    </application>

</manifest>
