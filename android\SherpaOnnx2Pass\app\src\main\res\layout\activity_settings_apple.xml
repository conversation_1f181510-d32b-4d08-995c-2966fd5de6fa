<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/apple_system_grouped_background"
    android:fitsSystemWindows="true">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/apple_system_grouped_background"
        app:elevation="0dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/apple_system_grouped_background"
            app:title="设置"
            app:titleTextColor="@color/apple_label"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:navigationIconTint="@color/apple_blue" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Settings Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- AI Settings Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="AI 设置"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                android:textColor="@color/apple_secondary_label"
                android:layout_marginBottom="8dp"
                android:layout_marginStart="16dp" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardBackgroundColor="@color/apple_secondary_system_grouped_background"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- LLM Provider Selection -->
                    <LinearLayout
                        android:id="@+id/ll_llm_provider"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp"
                        android:background="?attr/selectableItemBackground">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="AI 提供商"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body" />

                        <TextView
                            android:id="@+id/tv_current_provider"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Gemini"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                            android:textColor="@color/apple_secondary_label"
                            android:layout_marginEnd="8dp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_chevron_right"
                            android:tint="@color/apple_tertiary_label" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/apple_separator"
                        android:layout_marginStart="16dp" />

                    <!-- Auto Optimize Toggle -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="自动优化"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Body" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="停止录音后自动优化识别结果"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                                android:textColor="@color/apple_secondary_label" />

                        </LinearLayout>

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_auto_optimize"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/apple_blue" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Voice Settings Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="语音设置"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                android:textColor="@color/apple_secondary_label"
                android:layout_marginBottom="8dp"
                android:layout_marginStart="16dp" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardBackgroundColor="@color/apple_secondary_system_grouped_background"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Speaker Management -->
                    <LinearLayout
                        android:id="@+id/ll_speaker_management"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp"
                        android:background="?attr/selectableItemBackground">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="声纹管理"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body" />

                        <TextView
                            android:id="@+id/tv_speaker_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 人"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                            android:textColor="@color/apple_secondary_label"
                            android:layout_marginEnd="8dp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_chevron_right"
                            android:tint="@color/apple_tertiary_label" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/apple_separator"
                        android:layout_marginStart="16dp" />

                    <!-- Save Recording Toggle -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="保存录音"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Body" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="录音时同时保存音频文件"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                                android:textColor="@color/apple_secondary_label" />

                        </LinearLayout>

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_save_recording"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/apple_blue" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/apple_separator"
                        android:layout_marginStart="16dp" />

                    <!-- Export Format -->
                    <LinearLayout
                        android:id="@+id/ll_export_format"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp"
                        android:background="?attr/selectableItemBackground">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="导出格式"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body" />

                        <TextView
                            android:id="@+id/tv_export_format"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="TXT"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                            android:textColor="@color/apple_secondary_label"
                            android:layout_marginEnd="8dp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_chevron_right"
                            android:tint="@color/apple_tertiary_label" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Floating Window Settings Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="悬浮窗设置"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                android:textColor="@color/apple_secondary_label"
                android:layout_marginBottom="8dp"
                android:layout_marginStart="16dp" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardBackgroundColor="@color/apple_secondary_system_grouped_background"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Enable Floating Window -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="启用悬浮窗"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Body" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="在其他应用上方显示录音按钮"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                                android:textColor="@color/apple_secondary_label" />

                        </LinearLayout>

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_floating_window"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/apple_blue" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/apple_separator"
                        android:layout_marginStart="16dp" />

                    <!-- Auto Start Floating Window -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="自动启动"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Body" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="打开应用时自动显示悬浮窗"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                                android:textColor="@color/apple_secondary_label" />

                        </LinearLayout>

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_auto_start_floating"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/apple_blue" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/apple_separator"
                        android:layout_marginStart="16dp" />

                    <!-- Edge Snap -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="边缘吸附"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Body" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="拖拽后自动贴边显示"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                                android:textColor="@color/apple_secondary_label" />

                        </LinearLayout>

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_edge_snap"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            app:thumbTint="@color/white"
                            app:trackTint="@color/apple_blue" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- About Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="关于"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                android:textColor="@color/apple_secondary_label"
                android:layout_marginBottom="8dp"
                android:layout_marginStart="16dp" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardBackgroundColor="@color/apple_secondary_system_grouped_background"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Version Info -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="16dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="版本"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body" />

                        <TextView
                            android:id="@+id/tv_version"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1.0.0"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                            android:textColor="@color/apple_secondary_label" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
