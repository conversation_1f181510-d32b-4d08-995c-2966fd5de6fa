package com.k2fsa.sherpa.onnx

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.appcompat.app.AlertDialog

/**
 * 悬浮窗权限管理器
 * 处理悬浮窗权限的检查、申请和管理
 */
object FloatingWindowPermissionManager {
    
    private const val TAG = "FloatingWindowPermissionManager"
    private const val REQUEST_CODE_OVERLAY_PERMISSION = 1001
    
    /**
     * 检查是否有悬浮窗权限
     */
    fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true // Android 6.0以下默认有权限
        }
    }
    
    /**
     * 请求悬浮窗权限
     */
    fun requestOverlayPermission(activity: Activity, callback: PermissionCallback? = null) {
        if (hasOverlayPermission(activity)) {
            Log.d(TAG, "悬浮窗权限已授予")
            callback?.onPermissionGranted()
            return
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            showPermissionExplanationDialog(activity) {
                try {
                    val intent = Intent(
                        Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:${activity.packageName}")
                    )
                    activity.startActivityForResult(intent, REQUEST_CODE_OVERLAY_PERMISSION)
                    Log.d(TAG, "已启动悬浮窗权限申请页面")
                } catch (e: Exception) {
                    Log.e(TAG, "启动权限申请页面失败", e)
                    callback?.onPermissionDenied("无法打开权限设置页面: ${e.message}")
                }
            }
        } else {
            callback?.onPermissionGranted()
        }
    }
    
    /**
     * 显示权限说明对话框
     */
    private fun showPermissionExplanationDialog(activity: Activity, onConfirm: () -> Unit) {
        AlertDialog.Builder(activity)
            .setTitle("🎈 悬浮窗权限")
            .setMessage(
                "为了让您随时随地方便录音，应用需要悬浮窗权限。\n\n" +
                "悬浮窗功能：\n" +
                "• 🎤 一键快速录音\n" +
                "• 📱 不影响其他应用使用\n" +
                "• 🔄 实时同步录音状态\n" +
                "• 📍 智能边缘吸附\n\n" +
                "点击「授予权限」将跳转到系统设置页面，请开启「显示在其他应用的上层」权限。"
            )
            .setPositiveButton("授予权限") { _, _ ->
                onConfirm()
            }
            .setNegativeButton("暂不开启") { _, _ ->
                Log.d(TAG, "用户拒绝授予悬浮窗权限")
            }
            .setCancelable(false)
            .show()
    }
    
    /**
     * 处理权限申请结果
     */
    fun handlePermissionResult(
        activity: Activity,
        requestCode: Int,
        callback: PermissionCallback?
    ) {
        if (requestCode == REQUEST_CODE_OVERLAY_PERMISSION) {
            if (hasOverlayPermission(activity)) {
                Log.i(TAG, "悬浮窗权限申请成功")
                callback?.onPermissionGranted()
            } else {
                Log.w(TAG, "悬浮窗权限申请失败")
                callback?.onPermissionDenied("用户拒绝授予悬浮窗权限")
            }
        }
    }
    
    /**
     * 显示权限被拒绝的提示
     */
    fun showPermissionDeniedDialog(activity: Activity, onRetry: (() -> Unit)? = null) {
        AlertDialog.Builder(activity)
            .setTitle("⚠️ 权限未授予")
            .setMessage(
                "悬浮窗权限未授予，无法使用悬浮录音功能。\n\n" +
                "您可以：\n" +
                "• 重新申请权限\n" +
                "• 稍后在设置中手动开启\n" +
                "• 继续使用应用内录音功能"
            )
            .setPositiveButton("重新申请") { _, _ ->
                onRetry?.invoke()
            }
            .setNegativeButton("稍后再说", null)
            .show()
    }
    
    /**
     * 权限回调接口
     */
    interface PermissionCallback {
        fun onPermissionGranted()
        fun onPermissionDenied(reason: String)
    }
}
