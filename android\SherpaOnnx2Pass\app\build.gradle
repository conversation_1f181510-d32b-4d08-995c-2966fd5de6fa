plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.k2fsa.sherpa.onnx'
    compileSdk 34

    defaultConfig {
        applicationId "com.k2fsa.sherpa.onnx"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // 从 gradle.properties 读取 API 密钥
        def geminiApiKey = project.findProperty("GEMINI_API_KEY") ?: ""
        buildConfigField "String", "GEMINI_API_KEY", "\"${geminiApiKey}\""
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.appcompat:appcompat:1.5.1'
    implementation 'com.google.android.material:material:1.7.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    // 协程支持
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4'

    // Lifecycle支持 (用于lifecycleScope)
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.2'

    // JSON序列化支持 (用于会议记录数据持久化)
    implementation 'com.google.code.gson:gson:2.10.1'

    // RecyclerView支持 (用于会议记录列表)
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // CardView支持 (用于会议记录卡片布局)
    implementation 'androidx.cardview:cardview:1.0.0'

    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:4.11.0'
    testImplementation 'org.mockito:mockito-inline:4.11.0'
    androidTestImplementation 'androidx.test.ext:junit:1.1.4'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.0'
}