package com.k2fsa.sherpa.onnx

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * 悬浮窗设置管理器
 * 管理悬浮窗相关的用户设置和偏好
 */
object FloatingWindowSettings {
    
    private const val TAG = "FloatingWindowSettings"
    private const val PREFS_NAME = "floating_window_settings"
    
    // 设置键名
    private const val KEY_AUTO_START_ENABLED = "auto_start_enabled"
    private const val KEY_FLOATING_WINDOW_ENABLED = "floating_window_enabled"
    private const val KEY_LAST_POSITION_X = "last_position_x"
    private const val KEY_LAST_POSITION_Y = "last_position_y"
    private const val KEY_EDGE_SNAP_ENABLED = "edge_snap_enabled"
    private const val KEY_SHOW_MENU_ON_LONG_PRESS = "show_menu_on_long_press"
    
    /**
     * 获取SharedPreferences实例
     */
    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 是否启用悬浮窗功能
     */
    fun isFloatingWindowEnabled(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_FLOATING_WINDOW_ENABLED, false)
    }
    
    /**
     * 设置悬浮窗功能启用状态
     */
    fun setFloatingWindowEnabled(context: Context, enabled: Boolean) {
        getPrefs(context).edit()
            .putBoolean(KEY_FLOATING_WINDOW_ENABLED, enabled)
            .apply()
        Log.d(TAG, "悬浮窗功能设置为: $enabled")
    }
    
    /**
     * 是否启用自动启动悬浮窗
     */
    fun isAutoStartEnabled(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_AUTO_START_ENABLED, false)
    }
    
    /**
     * 设置自动启动悬浮窗
     */
    fun setAutoStartEnabled(context: Context, enabled: Boolean) {
        getPrefs(context).edit()
            .putBoolean(KEY_AUTO_START_ENABLED, enabled)
            .apply()
        Log.d(TAG, "自动启动悬浮窗设置为: $enabled")
    }
    
    /**
     * 获取上次悬浮窗位置X坐标
     */
    fun getLastPositionX(context: Context): Int {
        return getPrefs(context).getInt(KEY_LAST_POSITION_X, -1)
    }
    
    /**
     * 获取上次悬浮窗位置Y坐标
     */
    fun getLastPositionY(context: Context): Int {
        return getPrefs(context).getInt(KEY_LAST_POSITION_Y, -1)
    }
    
    /**
     * 保存悬浮窗位置
     */
    fun saveLastPosition(context: Context, x: Int, y: Int) {
        getPrefs(context).edit()
            .putInt(KEY_LAST_POSITION_X, x)
            .putInt(KEY_LAST_POSITION_Y, y)
            .apply()
        Log.d(TAG, "保存悬浮窗位置: ($x, $y)")
    }
    
    /**
     * 是否启用边缘吸附
     */
    fun isEdgeSnapEnabled(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_EDGE_SNAP_ENABLED, true)
    }
    
    /**
     * 设置边缘吸附
     */
    fun setEdgeSnapEnabled(context: Context, enabled: Boolean) {
        getPrefs(context).edit()
            .putBoolean(KEY_EDGE_SNAP_ENABLED, enabled)
            .apply()
        Log.d(TAG, "边缘吸附设置为: $enabled")
    }
    
    /**
     * 是否在长按时显示菜单
     */
    fun isShowMenuOnLongPressEnabled(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_SHOW_MENU_ON_LONG_PRESS, true)
    }
    
    /**
     * 设置长按显示菜单
     */
    fun setShowMenuOnLongPressEnabled(context: Context, enabled: Boolean) {
        getPrefs(context).edit()
            .putBoolean(KEY_SHOW_MENU_ON_LONG_PRESS, enabled)
            .apply()
        Log.d(TAG, "长按显示菜单设置为: $enabled")
    }
    
    /**
     * 重置所有设置为默认值
     */
    fun resetToDefaults(context: Context) {
        getPrefs(context).edit()
            .putBoolean(KEY_FLOATING_WINDOW_ENABLED, false)
            .putBoolean(KEY_AUTO_START_ENABLED, false)
            .putInt(KEY_LAST_POSITION_X, -1)
            .putInt(KEY_LAST_POSITION_Y, -1)
            .putBoolean(KEY_EDGE_SNAP_ENABLED, true)
            .putBoolean(KEY_SHOW_MENU_ON_LONG_PRESS, true)
            .apply()
        Log.d(TAG, "悬浮窗设置已重置为默认值")
    }
    
    /**
     * 获取所有设置的摘要信息
     */
    fun getSettingsSummary(context: Context): String {
        return buildString {
            append("悬浮窗设置摘要:\n")
            append("- 悬浮窗功能: ${if (isFloatingWindowEnabled(context)) "启用" else "禁用"}\n")
            append("- 自动启动: ${if (isAutoStartEnabled(context)) "启用" else "禁用"}\n")
            append("- 边缘吸附: ${if (isEdgeSnapEnabled(context)) "启用" else "禁用"}\n")
            append("- 长按菜单: ${if (isShowMenuOnLongPressEnabled(context)) "启用" else "禁用"}\n")
            val x = getLastPositionX(context)
            val y = getLastPositionY(context)
            if (x != -1 && y != -1) {
                append("- 上次位置: ($x, $y)")
            } else {
                append("- 上次位置: 未保存")
            }
        }
    }
}
